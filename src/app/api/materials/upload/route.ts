import { db } from '@/database/drizzle';
import { MaterialRepository } from '@/database/repository/material';
import { SpaceRepository } from '@/database/repository/space';
import { documents } from '@/database/schema';
import { getUser } from '@/lib/auth';
import { processMaterialFromR2Enhanced } from '@/lib/materials/processor';
import { getDocumentType } from '@/lib/materials/utils';
import { eq } from 'drizzle-orm';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { user } = await getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const files = body.files as { key: string; name: string; type: string; size: number }[];
    const spaceIds = body.spaceIds as string[];

    if (!files || files.length === 0) {
      return NextResponse.json({ error: 'No files uploaded' }, { status: 400 });
    }

    // Process all files in parallel
    const results = await Promise.all(
      files.map(async (file) => {
        const fileKey = file.key;
        let material: typeof documents.$inferSelect | null = null;

        try {
          // Set document status to processing
          material = await MaterialRepository.storeMaterial({
            fileName: file.name,
            fileType: getDocumentType(file.type),
            s3Key: fileKey,
            uploadedBy: user.id,
            summary: null, // Will be updated after processing
            summaryEmbedding: null, // Will be updated after processing
          });

          await MaterialRepository.updateMaterialStatus(material.id, 'processing');

          // Process material from R2 with enhanced OCR support
          const { chunks, summary, ocrAnalysis } = await processMaterialFromR2Enhanced(
            fileKey,
            file.name,
            file.type,
            true
          ); // Enable debug

          // Update material with summary data if available
          if (summary && typeof summary === 'object' && 'summary' in summary) {
            await db
              .update(documents)
              .set({
                summary: (summary as { summary: string }).summary || null,
                summaryEmbedding:
                  (summary as { summaryEmbedding: number[] | null }).summaryEmbedding || null,
              })
              .where(eq(documents.id, material.id));
          }

          // Store chunks with enhanced OCR data
          await MaterialRepository.storeChunksEnhanced({
            documentId: material.id,
            chunks: chunks.map((chunk) => ({
              ...chunk,
              content: chunk.content.replace(/\x00/g, ''), // Remove null bytes
            })),
          });

          // Store OCR analysis if available
          if (ocrAnalysis) {
            await MaterialRepository.storeOcrAnalysis(
              material.id,
              ocrAnalysis,
              0 // Processing time will be tracked separately in future
            );
          }

          // Mark document as completed
          await MaterialRepository.updateMaterialStatus(material.id, 'completed');

          // If spaceIds are provided, create document-space connections
          if (spaceIds.length > 0) {
            await SpaceRepository.addDocumentsToSpace([material.id], spaceIds);
          }

          return {
            success: true,
            fileName: file.name,
            materialId: material.id,
          };
        } catch (error) {
          console.error(`Error processing file ${file.name}:`, error);

          // If material was created, mark as failed and clean up
          if (material?.id) {
            try {
              await MaterialRepository.updateMaterialStatus(material.id, 'failed');
              await MaterialRepository.deleteMaterial(material.id, fileKey);
            } catch (cleanupError) {
              console.error(`Error during cleanup for file ${file.name}:`, cleanupError);
            }
          }

          return {
            success: false,
            fileName: file.name,
            error: error instanceof Error ? error.message : 'Error processing file',
          };
        }
      })
    );

    const successfulUploads = results.filter((result) => result.success);
    const failedUploads = results.filter((result) => !result.success);

    if (successfulUploads.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'All uploads failed',
          failedUploads,
        },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      uploadedFiles: successfulUploads.map((result) => result.fileName),
      failedUploads: failedUploads.length > 0 ? failedUploads : undefined,
    });
  } catch (error) {
    console.error('Error processing upload:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
