import { env } from '@/lib/env.mjs';
import {
  AnalyzeDocumentCommand,
  Block,
  FeatureType,
  Geometry,
  Point,
  TextractClient,
} from '@aws-sdk/client-textract';

export const textractClient = new TextractClient({
  region: env.AWS_REGION || 'us-east-1',
  credentials: {
    accessKeyId: env.AWS_ACCESS_KEY_ID,
    secretAccessKey: env.AWS_SECRET_ACCESS_KEY,
  },
});

export interface TextractGeometry {
  boundingBox: {
    width: number;
    height: number;
    left: number;
    top: number;
  };
  polygon: Array<{ x: number; y: number }>;
}

export interface TextractExtractedText {
  text: string;
  confidence: number;
  geometry: TextractGeometry;
  layoutType: string;
  blockId: string;
  pageIndex: number;
}

export interface TextractResult {
  extractedText: TextractExtractedText[];
  layoutElements: {
    paragraphs: TextractExtractedText[];
    titles: TextractExtractedText[];
    headers: TextractExtractedText[];
    footers: TextractExtractedText[];
    sectionHeaders: TextractExtractedText[];
  };
  rawBlocks: Block[];
}

/**
 * Map AWS Textract block types to our database enum values
 */
function mapTextractBlockTypeToLayoutType(blockType: string): string {
  const blockTypeMap: Record<string, string> = {
    WORD: 'LAYOUT_TEXT',
    LINE: 'LINE',
    LAYOUT_TEXT: 'LAYOUT_TEXT',
    LAYOUT_TITLE: 'TITLE',
    LAYOUT_HEADER: 'HEADER',
    LAYOUT_FOOTER: 'FOOTER',
    LAYOUT_SECTION_HEADER: 'SECTION_HEADER',
    LAYOUT_LIST: 'PARAGRAPH',
    LAYOUT_FIGURE: 'PARAGRAPH',
    LAYOUT_TABLE: 'PARAGRAPH',
    LAYOUT_KEY_VALUE: 'PARAGRAPH',
    PAGE: 'UNKNOWN',
    MERGED_CELL: 'UNKNOWN',
    SELECTION_ELEMENT: 'UNKNOWN',
  };

  return blockTypeMap[blockType] || 'UNKNOWN';
}

/**
 * Convert Textract geometry to our standard format
 */
function convertGeometry(geometry: Geometry): TextractGeometry {
  const boundingBox = geometry?.BoundingBox || {};
  const polygon = geometry?.Polygon || [];

  return {
    boundingBox: {
      width: boundingBox.Width || 0,
      height: boundingBox.Height || 0,
      left: boundingBox.Left || 0,
      top: boundingBox.Top || 0,
    },
    polygon: polygon.map((point: Point) => ({
      x: point.X || 0,
      y: point.Y || 0,
    })),
  };
}

/**
 * Extract text from a PDF document using AWS Textract with LAYOUT analysis
 * @param documentBytes Buffer containing the PDF document
 * @param debug Whether to log detailed processing information
 * @returns Extracted text with layout and geometry information
 */
export async function analyzeDocumentWithTextract(
  documentBytes: Buffer,
  debug = false
): Promise<TextractResult> {
  if (debug) {
    console.log('🔍 Starting Textract analysis...');
    console.log(`Document size: ${(documentBytes.length / 1024 / 1024).toFixed(2)} MB`);
  }

  const startTime = performance.now();

  try {
    const command = new AnalyzeDocumentCommand({
      Document: {
        Bytes: documentBytes,
      },
      FeatureTypes: [FeatureType.LAYOUT],
    });

    const response = await textractClient.send(command);
    const endTime = performance.now();

    if (debug) {
      console.log(
        `✅ Textract analysis completed in ${((endTime - startTime) / 1000).toFixed(2)}s`
      );
      console.log(`Found ${response.Blocks?.length || 0} blocks`);
    }

    if (!response.Blocks) {
      throw new Error('No blocks returned from Textract');
    }

    const extractedText: TextractExtractedText[] = [];
    const layoutElements = {
      paragraphs: [] as TextractExtractedText[],
      titles: [] as TextractExtractedText[],
      headers: [] as TextractExtractedText[],
      footers: [] as TextractExtractedText[],
      sectionHeaders: [] as TextractExtractedText[],
    };

    // Process blocks and extract text with layout information
    for (const block of response.Blocks) {
      if (!block.Text || !block.Confidence || !block.Geometry) {
        continue;
      }

      // Extract page index from block metadata (Textract provides page info)
      const pageIndex = (block.Page || 1) - 1; // Convert to 0-based indexing

      const extractedItem: TextractExtractedText = {
        text: block.Text,
        confidence: block.Confidence / 100, // Convert to 0-1 scale
        geometry: convertGeometry(block.Geometry),
        layoutType: mapTextractBlockTypeToLayoutType(block.BlockType || ''),
        blockId: block.Id || '',
        pageIndex,
      };

      console.log('extractedItem: ', JSON.stringify(extractedItem, null, 2));

      extractedText.push(extractedItem);

      // Categorize by mapped layout type
      switch (extractedItem.layoutType) {
        case 'LAYOUT_TEXT':
          // For layout text, check if it's part of a specific layout element
          if (block.Text.length > 50) {
            layoutElements.paragraphs.push(extractedItem);
          }
          break;
        case 'TITLE':
          layoutElements.titles.push(extractedItem);
          break;
        case 'HEADER':
          layoutElements.headers.push(extractedItem);
          break;
        case 'FOOTER':
          layoutElements.footers.push(extractedItem);
          break;
        case 'SECTION_HEADER':
          layoutElements.sectionHeaders.push(extractedItem);
          break;
        case 'LINE':
          // Lines are individual text lines, group them as paragraphs if they're substantial
          if (block.Text.length > 20) {
            layoutElements.paragraphs.push(extractedItem);
          }
          break;
        case 'PARAGRAPH':
          layoutElements.paragraphs.push(extractedItem);
          break;
      }
    }

    if (debug) {
      console.log('📊 Textract extraction summary:');
      console.log(`  - Total text blocks: ${extractedText.length}`);
      console.log(`  - Paragraphs: ${layoutElements.paragraphs.length}`);
      console.log(`  - Titles: ${layoutElements.titles.length}`);
      console.log(`  - Headers: ${layoutElements.headers.length}`);
      console.log(`  - Footers: ${layoutElements.footers.length}`);
      console.log(`  - Section headers: ${layoutElements.sectionHeaders.length}`);

      // Show confidence distribution
      const confidences = extractedText.map((item) => item.confidence);
      const avgConfidence = confidences.reduce((a, b) => a + b, 0) / confidences.length;
      const minConfidence = Math.min(...confidences);
      const maxConfidence = Math.max(...confidences);

      console.log(
        `  - Confidence: avg=${(avgConfidence * 100).toFixed(1)}%, min=${(minConfidence * 100).toFixed(1)}%, max=${(maxConfidence * 100).toFixed(1)}%`
      );
    }

    return {
      extractedText,
      layoutElements,
      rawBlocks: response.Blocks,
    };
  } catch (error) {
    console.error('❌ Textract analysis failed:', error);
    throw new Error(
      `Textract analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

/**
 * Check if AWS Textract service is available and properly configured
 */
export async function checkTextractAvailability(): Promise<boolean> {
  try {
    // Simple test to verify credentials and service availability
    const testCommand = new AnalyzeDocumentCommand({
      Document: {
        Bytes: Buffer.from('test'),
      },
      FeatureTypes: [FeatureType.LAYOUT],
    });

    // This will fail with invalid document but should not fail with auth errors
    await textractClient.send(testCommand);
    return true;
  } catch (error: unknown) {
    // If it's an invalid document error, credentials are working
    const errorName = error instanceof Error ? error.name : 'Unknown';
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    if (errorName === 'InvalidParameterException' || errorName === 'UnsupportedDocumentException') {
      return true;
    }

    // Other errors indicate service unavailability or auth issues
    console.warn('Textract service unavailable:', errorMessage);
    return false;
  }
}

/**
 * Process Textract blocks into semantic chunks using LAYOUT_* blocks
 * This creates chunks based on semantic document structure (paragraphs, titles, headers)
 */
export function processTextractBlocksToChunks(result: TextractResult): Array<{
  content: string;
  chunkOrder: number;
  geometry: TextractGeometry[];
  layoutTypes: string[];
  confidenceScore: number;
}> {
  const chunks = [];
  const rawBlocks = result.rawBlocks;

  // Create a map of block ID to block for quick lookups
  const blocksMap = new Map<string, Block>();
  rawBlocks.forEach((block) => {
    if (block.Id) {
      blocksMap.set(block.Id, block);
    }
  });

  // Find all LAYOUT_* blocks (semantic containers)
  const layoutBlocks = rawBlocks.filter(
    (block) =>
      block.BlockType?.startsWith('LAYOUT_') &&
      ['LAYOUT_TITLE', 'LAYOUT_HEADER', 'LAYOUT_SECTION_HEADER', 'LAYOUT_TEXT'].includes(
        block.BlockType
      )
  );

  // Sort layout blocks by position (top to bottom, left to right)
  const sortedLayoutBlocks = layoutBlocks.sort((a, b) => {
    if (!a.Geometry?.BoundingBox || !b.Geometry?.BoundingBox) return 0;
    const aTop = a.Geometry.BoundingBox.Top ?? 0;
    const bTop = b.Geometry.BoundingBox.Top ?? 0;
    const aLeft = a.Geometry.BoundingBox.Left ?? 0;
    const bLeft = b.Geometry.BoundingBox.Left ?? 0;
    const topDiff = aTop - bTop;
    if (Math.abs(topDiff) < 0.01) {
      return aLeft - bLeft;
    }
    return topDiff;
  });

  let chunkOrder = 0;

  for (const layoutBlock of sortedLayoutBlocks) {
    const semanticChunk = extractTextFromLayoutBlock(layoutBlock, blocksMap);

    if (semanticChunk.text.trim().length > 0) {
      chunks.push({
        content: semanticChunk.text.trim(),
        chunkOrder: chunkOrder++,
        geometry: semanticChunk.geometry,
        layoutTypes: [semanticChunk.layoutType],
        confidenceScore: semanticChunk.confidence,
      });
    }
  }

  return chunks;
}

/**
 * Extract text and combine geometry from a LAYOUT_* block by following its child LINE relationships
 */
function extractTextFromLayoutBlock(
  layoutBlock: Block,
  blocksMap: Map<string, Block>
): {
  text: string;
  geometry: TextractGeometry[];
  layoutType: string;
  confidence: number;
} {
  const lineTexts: string[] = [];
  const geometries: TextractGeometry[] = [];
  const confidences: number[] = [];

  // Map layout block type to simplified type
  const layoutType = mapLayoutType(layoutBlock.BlockType || 'UNKNOWN');

  // Get child LINE blocks through relationships
  if (layoutBlock.Relationships) {
    for (const relationship of layoutBlock.Relationships) {
      if (relationship.Type === 'CHILD' && relationship.Ids) {
        for (const lineId of relationship.Ids) {
          const lineBlock = blocksMap.get(lineId);
          if (lineBlock && lineBlock.BlockType === 'LINE') {
            // Add the line text
            if (lineBlock.Text) {
              lineTexts.push(lineBlock.Text);
            }

            // Add the line geometry
            if (lineBlock.Geometry?.BoundingBox && lineBlock.Geometry?.Polygon) {
              geometries.push({
                boundingBox: {
                  left: lineBlock.Geometry.BoundingBox.Left || 0,
                  top: lineBlock.Geometry.BoundingBox.Top || 0,
                  width: lineBlock.Geometry.BoundingBox.Width || 0,
                  height: lineBlock.Geometry.BoundingBox.Height || 0,
                },
                polygon: lineBlock.Geometry.Polygon.map((point) => ({
                  x: point.X || 0,
                  y: point.Y || 0,
                })),
              });
            }

            // Add confidence
            if (lineBlock.Confidence) {
              confidences.push(lineBlock.Confidence);
            }
          }
        }
      }
    }
  }

  return {
    text: lineTexts.join(' '),
    geometry: geometries,
    layoutType,
    confidence:
      confidences.length > 0 ? confidences.reduce((a, b) => a + b, 0) / confidences.length : 1.0,
  };
}

/**
 * Map Textract layout types to simpler semantic types
 */
function mapLayoutType(blockType: string): string {
  switch (blockType) {
    case 'LAYOUT_TITLE':
      return 'TITLE';
    case 'LAYOUT_HEADER':
      return 'HEADER';
    case 'LAYOUT_SECTION_HEADER':
      return 'SECTION_HEADER';
    case 'LAYOUT_TEXT':
      return 'PARAGRAPH';
    default:
      return 'TEXT';
  }
}
