'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useDocumentHighlights } from '@/hooks/use-document-highlights';
import { Copy, MessageSquare, X } from 'lucide-react';
import { toast } from 'sonner';
import { useCallback, useState } from 'react';
import { EnhancedPDFViewer, type OCRHighlight } from './enhanced-pdf-viewer';

interface DocumentViewerEnhancedProps {
  documentId: string;
  documentUrl: string;
  fileName: string;
  onTextReference?: (text: string, context?: Record<string, unknown>) => void;
  className?: string;
}

export function DocumentViewerEnhanced({
  documentId,
  documentUrl,
  fileName,
  onTextReference,
  className,
}: DocumentViewerEnhancedProps) {
  const [selectedHighlight, setSelectedHighlight] = useState<OCRHighlight | null>(null);

  const { highlights } = useDocumentHighlights(documentId);

  const handleHighlightClick = useCallback((highlight: OCRHighlight) => {
    setSelectedHighlight(highlight);

    // Auto-scroll to highlight if needed
    console.log('Clicked highlight:', {
      text: highlight.text.substring(0, 100) + '...',
      confidence: highlight.confidence,
      layoutType: highlight.layoutType,
    });
  }, []);

  const handleHighlightHover = useCallback(() => {
    // Could show preview tooltip here
  }, []);

  const copyHighlightText = useCallback(() => {
    if (selectedHighlight) {
      navigator.clipboard.writeText(selectedHighlight.text);
      toast.success('Text copied to clipboard');
    }
  }, [selectedHighlight]);

  const referenceInChat = useCallback(() => {
    if (selectedHighlight && onTextReference) {
      onTextReference(selectedHighlight.text, {
        documentId,
        fileName,
        layoutType: selectedHighlight.layoutType,
        confidence: selectedHighlight.confidence,
      });
      toast.success('Text referenced in chat');
    }
  }, [selectedHighlight, onTextReference, documentId, fileName]);

  const clearSelection = useCallback(() => {
    setSelectedHighlight(null);
  }, []);

  return (
    <div className={`h-full bg-gray-50 ${className}`}>
      {/* PDF Viewer */}
      <div className="relative h-full">
        {/* Enhanced PDF Viewer */}
        <EnhancedPDFViewer
          url={documentUrl}
          documentId={documentId}
          highlights={highlights}
          onHighlightClick={handleHighlightClick}
          onHighlightHover={handleHighlightHover}
          enableInteractions={true}
        />

        {/* Selected Highlight Panel */}
        {selectedHighlight && (
          <Card className="absolute bottom-4 left-4 right-4 z-50 mx-auto max-w-2xl shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-start justify-between gap-4">
                <div className="flex-1 space-y-2">
                  {/* Header */}
                  <div className="flex items-center gap-2">
                    <Badge
                      variant={
                        selectedHighlight.confidence >= 0.9
                          ? 'default'
                          : selectedHighlight.confidence >= 0.7
                            ? 'secondary'
                            : 'destructive'
                      }
                      className="text-xs"
                    >
                      {selectedHighlight.layoutType}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {Math.round(selectedHighlight.confidence * 100)}% confidence
                    </Badge>
                  </div>

                  {/* Content */}
                  <div className="max-h-32 overflow-y-auto text-sm leading-relaxed text-gray-800">
                    {selectedHighlight.text}
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2 pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={copyHighlightText}
                      className="text-xs"
                    >
                      <Copy className="mr-1 h-3 w-3" />
                      Copy
                    </Button>

                    {onTextReference && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={referenceInChat}
                        className="text-xs"
                      >
                        <MessageSquare className="mr-1 h-3 w-3" />
                        Reference in Chat
                      </Button>
                    )}
                  </div>
                </div>

                {/* Close button */}
                <Button variant="ghost" size="sm" onClick={clearSelection} className="h-auto p-1">
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
