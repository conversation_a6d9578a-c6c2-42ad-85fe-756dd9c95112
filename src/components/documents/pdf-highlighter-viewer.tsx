'use client';

import { Pdf<PERSON><PERSON><PERSON><PERSON>, Pd<PERSON><PERSON><PERSON><PERSON>, Pop<PERSON> } from 'react-pdf-highlighter';
import type {
  Content,
  IHighlight,
  LTWH,
  LTWHP,
  Position,
  Scaled,
  ScaledPosition,
} from 'react-pdf-highlighter';
import React, { useCallback, useState } from 'react';
import { OCRHighlight } from './enhanced-pdf-viewer';

type ViewportHighlight = IHighlight & { position: Position };

interface PDFHighlighterViewerProps {
  url: string;
  highlights: OCRHighlight[];
  onHighlightClick?: (highlight: OCRHighlight) => void;
  onHighlightHover?: (highlight: OCRHighlight | null) => void;
  showHighlights?: boolean;
}

// Helper function to convert OCR highlights to react-pdf-highlighter format
function convertOCRToHighlighterFormat(ocrHighlights: OCRHighlight[]): IHighlight[] {
  return ocrHighlights.map((ocrHighlight) => {
    const { boundingBox, pageIndex, text, layoutType } = ocrHighlight;

    // Convert normalized coordinates (0-1) to viewport coordinates
    // react-pdf-highlighter expects normalized coordinates (0-1) for boundingRect
    // but actual page dimensions for the rects
    const scaledPosition: ScaledPosition = {
      boundingRect: {
        x1: boundingBox.left,
        y1: boundingBox.top,
        x2: boundingBox.left + boundingBox.width,
        y2: boundingBox.top + boundingBox.height,
        width: 1,
        height: 1,
      },
      rects: [
        {
          x1: boundingBox.left,
          y1: boundingBox.top,
          x2: boundingBox.left + boundingBox.width,
          y2: boundingBox.top + boundingBox.height,
          width: 1,
          height: 1,
          pageNumber: pageIndex + 1, // react-pdf-highlighter uses 1-based page numbers
        },
      ],
      pageNumber: pageIndex + 1,
    };

    const content: Content = {
      text: text,
    };

    return {
      id: ocrHighlight.id,
      position: scaledPosition,
      content,
      comment: {
        text: layoutType,
        emoji: '',
      },
    };
  });
}

const HighlightPopup = ({ comment }: { comment: { text: string; emoji: string } }) =>
  comment.text ? (
    <div className="max-w-xs rounded border border-gray-200 bg-white p-2 text-sm shadow-lg">
      {comment.text}
    </div>
  ) : null;

// Custom Highlight component that supports hover-only visibility
const HoverHighlight = ({
  position,
  isHovered
}: {
  position: { boundingRect: { x1: number; y1: number; x2: number; y2: number } };
  isHovered: boolean;
}) => {
  return (
    <div
      style={{
        position: 'absolute',
        left: `${position.boundingRect.x1 * 100}%`,
        top: `${position.boundingRect.y1 * 100}%`,
        width: `${(position.boundingRect.x2 - position.boundingRect.x1) * 100}%`,
        height: `${(position.boundingRect.y2 - position.boundingRect.y1) * 100}%`,
        backgroundColor: isHovered ? 'rgba(255, 235, 59, 0.3)' : 'transparent',
        border: isHovered ? '1px solid rgba(255, 193, 7, 0.8)' : '1px solid transparent',
        borderRadius: '2px',
        transition: 'all 0.2s ease-in-out',
        pointerEvents: 'none',
        zIndex: 1,
      }}
    />
  );
};

export function PDFHighlighterViewer({
  url,
  highlights,
  onHighlightClick,
  onHighlightHover,
  showHighlights = true,
}: PDFHighlighterViewerProps) {
  const [pdfHighlights, setPdfHighlights] = useState<IHighlight[]>(() =>
    showHighlights ? convertOCRToHighlighterFormat(highlights) : []
  );

  // Track which highlight is currently being hovered
  const [hoveredHighlightId, setHoveredHighlightId] = useState<string | null>(null);

  // Update highlights when props change
  React.useEffect(() => {
    setPdfHighlights(showHighlights ? convertOCRToHighlighterFormat(highlights) : []);
  }, [highlights, showHighlights]);

  const handleHighlightTransform = useCallback(
    (
      highlight: ViewportHighlight,
      index: number,
      setTip: (
        highlight: ViewportHighlight,
        callback: (highlight: ViewportHighlight) => JSX.Element
      ) => void,
      hideTip: () => void,
      _viewportToScaled: (rect: LTWHP) => Scaled,
      _screenshot: (position: LTWH) => string,
      _isScrolledTo: boolean
    ) => {
      const isTextHighlight = !highlight.content?.image;
      const isHovered = hoveredHighlightId === highlight.id;

      const component = isTextHighlight ? (
        <HoverHighlight
          position={{
            boundingRect: highlight.position.boundingRect,
          }}
          isHovered={isHovered}
        />
      ) : null;

      return (
        <Popup
          popupContent={<HighlightPopup comment={highlight.comment} />}
          onMouseOver={(popupContent) => {
            setTip(highlight, () => popupContent);
            setHoveredHighlightId(highlight.id);
            // Find the corresponding OCR highlight and call the hover handler
            const ocrHighlight = highlights.find((h) => h.id === highlight.id);
            if (ocrHighlight && onHighlightHover) {
              onHighlightHover(ocrHighlight);
            }
          }}
          onMouseOut={() => {
            hideTip();
            setHoveredHighlightId(null);
            if (onHighlightHover) {
              onHighlightHover(null);
            }
          }}
          key={index}
        >
          <div
            onClick={() => {
              // Find the corresponding OCR highlight and call the click handler
              const ocrHighlight = highlights.find((h) => h.id === highlight.id);
              if (ocrHighlight && onHighlightClick) {
                onHighlightClick(ocrHighlight);
              }
            }}
            className="cursor-pointer"
          >
            {component}
          </div>
        </Popup>
      );
    },
    [highlights, onHighlightClick, onHighlightHover, hoveredHighlightId, setHoveredHighlightId]
  );

  return (
    <div className="h-full w-full">
      <PdfLoader
        url={url}
        beforeLoad={<div className="flex h-full items-center justify-center">Loading PDF...</div>}
      >
        {(pdfDocument) => (
          <PdfHighlighter
            pdfDocument={pdfDocument}
            enableAreaSelection={() => false} // Disable manual selection for now
            onScrollChange={() => {}}
            scrollRef={() => {}}
            onSelectionFinished={() => null} // No manual highlighting for now
            highlightTransform={handleHighlightTransform}
            highlights={pdfHighlights}
          />
        )}
      </PdfLoader>
    </div>
  );
}
