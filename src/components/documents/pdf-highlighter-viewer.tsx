'use client';

// import { Pdf<PERSON>ighlight<PERSON>, PdfLoader } from 'react-pdf-highlighter';
import type {
  Content,
  IHighlight,
  LTWH,
  LTWHP,
  Position,
  Scaled,
  ScaledPosition,
} from 'react-pdf-highlighter';
import React, { useCallback, useState } from 'react';
import { OCRHighlight } from './enhanced-pdf-viewer';

type ViewportHighlight = IHighlight & { position: Position };

interface PDFHighlighterViewerProps {
  url: string;
  highlights: OCRHighlight[];
  onHighlightClick?: (highlight: OCRHighlight) => void;
  onHighlightHover?: (highlight: OCRHighlight | null) => void;
  showHighlights?: boolean;
}

// Helper function to convert OCR highlights to react-pdf-highlighter format
function convertOCRToHighlighterFormat(ocrHighlights: OCRHighlight[]): IHighlight[] {
  return ocrHighlights.map((ocrHighlight) => {
    const { boundingBox, pageIndex, text, layoutType } = ocrHighlight;

    // Convert normalized coordinates (0-1) to viewport coordinates
    // react-pdf-highlighter expects normalized coordinates (0-1) for boundingRect
    // but actual page dimensions for the rects
    const scaledPosition: ScaledPosition = {
      boundingRect: {
        x1: boundingBox.left,
        y1: boundingBox.top,
        x2: boundingBox.left + boundingBox.width,
        y2: boundingBox.top + boundingBox.height,
        width: 1,
        height: 1,
      },
      rects: [
        {
          x1: boundingBox.left,
          y1: boundingBox.top,
          x2: boundingBox.left + boundingBox.width,
          y2: boundingBox.top + boundingBox.height,
          width: 1,
          height: 1,
          pageNumber: pageIndex + 1, // react-pdf-highlighter uses 1-based page numbers
        },
      ],
      pageNumber: pageIndex + 1,
    };

    const content: Content = {
      text: text,
    };

    return {
      id: ocrHighlight.id,
      position: scaledPosition,
      content,
      comment: {
        text: layoutType,
        emoji: '',
      },
    };
  });
}



// Custom Highlight component that supports hover-only visibility
const HoverHighlight = ({
  position,
  isHovered
}: {
  position: { boundingRect: { x1: number; y1: number; x2: number; y2: number } };
  isHovered: boolean;
}) => {
  return (
    <div
      style={{
        position: 'absolute',
        left: `${position.boundingRect.x1 * 100}%`,
        top: `${position.boundingRect.y1 * 100}%`,
        width: `${(position.boundingRect.x2 - position.boundingRect.x1) * 100}%`,
        height: `${(position.boundingRect.y2 - position.boundingRect.y1) * 100}%`,
        backgroundColor: isHovered ? 'rgba(255, 235, 59, 0.3)' : 'transparent',
        border: isHovered ? '1px solid rgba(255, 193, 7, 0.8)' : '1px solid transparent',
        borderRadius: '2px',
        transition: 'all 0.2s ease-in-out',
        pointerEvents: 'none',
        zIndex: 1,
      }}
    />
  );
};
