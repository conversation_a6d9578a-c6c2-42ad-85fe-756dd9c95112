'use client';

import { <PERSON><PERSON>, Pdf<PERSON><PERSON><PERSON><PERSON>, Pdf<PERSON><PERSON><PERSON>, Popup } from 'react-pdf-highlighter';
import type {
  Content,
  IHighlight,
  LTWH,
  LTWHP,
  Position,
  Scaled,
  ScaledPosition,
} from 'react-pdf-highlighter';
import React, { useCallback, useState } from 'react';
import { OCRHighlight } from './enhanced-pdf-viewer';

type ViewportHighlight = IHighlight & { position: Position };

interface PDFHighlighterViewerProps {
  url: string;
  highlights: OCRHighlight[];
  onHighlightClick?: (highlight: OCRHighlight) => void;
  onHighlightHover?: (highlight: OCRHighlight | null) => void;
  showHighlights?: boolean;
}

// Helper function to convert OCR highlights to react-pdf-highlighter format
function convertOCRToHighlighterFormat(ocrHighlights: OCRHighlight[]): IHighlight[] {
  return ocrHighlights.map((ocrHighlight) => {
    const { boundingBox, pageIndex, text, layoutType } = ocrHighlight;

    // Convert normalized coordinates (0-1) to viewport coordinates
    // react-pdf-highlighter expects normalized coordinates (0-1) for boundingRect
    // but actual page dimensions for the rects
    const scaledPosition: ScaledPosition = {
      boundingRect: {
        x1: boundingBox.left,
        y1: boundingBox.top,
        x2: boundingBox.left + boundingBox.width,
        y2: boundingBox.top + boundingBox.height,
        width: 1,
        height: 1,
      },
      rects: [
        {
          x1: boundingBox.left,
          y1: boundingBox.top,
          x2: boundingBox.left + boundingBox.width,
          y2: boundingBox.top + boundingBox.height,
          width: 1,
          height: 1,
          pageNumber: pageIndex + 1, // react-pdf-highlighter uses 1-based page numbers
        },
      ],
      pageNumber: pageIndex + 1,
    };

    const content: Content = {
      text: text,
    };

    return {
      id: ocrHighlight.id,
      position: scaledPosition,
      content,
      comment: {
        text: layoutType,
        emoji: '',
      },
    };
  });
}

const HighlightPopup = ({ comment }: { comment: { text: string; emoji: string } }) =>
  comment.text ? (
    <div className="max-w-xs rounded border border-gray-200 bg-white p-2 text-sm shadow-lg">
      {comment.text}
    </div>
  ) : null;

export function PDFHighlighterViewer({
  url,
  highlights,
  onHighlightClick,
  onHighlightHover,
  showHighlights = true,
}: PDFHighlighterViewerProps) {
  const [pdfHighlights, setPdfHighlights] = useState<IHighlight[]>(() =>
    showHighlights ? convertOCRToHighlighterFormat(highlights) : []
  );

  // Update highlights when props change
  React.useEffect(() => {
    setPdfHighlights(showHighlights ? convertOCRToHighlighterFormat(highlights) : []);
  }, [highlights, showHighlights]);

  const handleHighlightTransform = useCallback(
    (
      highlight: ViewportHighlight,
      index: number,
      setTip: (
        highlight: ViewportHighlight,
        callback: (highlight: ViewportHighlight) => JSX.Element
      ) => void,
      hideTip: () => void,
      viewportToScaled: (rect: LTWHP) => Scaled,
      screenshot: (position: LTWH) => string,
      isScrolledTo: boolean
    ) => {
      const isTextHighlight = !highlight.content?.image;

      const component = isTextHighlight ? (
        <Highlight
          isScrolledTo={isScrolledTo}
          position={{
            boundingRect: highlight.position.boundingRect,
            rects: highlight.position.rects,
          }}
          comment={highlight.comment}
        />
      ) : null;

      return (
        <Popup
          popupContent={<HighlightPopup comment={highlight.comment} />}
          onMouseOver={(popupContent) => {
            setTip(highlight, () => popupContent);
            // Find the corresponding OCR highlight and call the hover handler
            const ocrHighlight = highlights.find((h) => h.id === highlight.id);
            if (ocrHighlight && onHighlightHover) {
              onHighlightHover(ocrHighlight);
            }
          }}
          onMouseOut={() => {
            hideTip();
            if (onHighlightHover) {
              onHighlightHover(null);
            }
          }}
          key={index}
        >
          <div
            onClick={() => {
              // Find the corresponding OCR highlight and call the click handler
              const ocrHighlight = highlights.find((h) => h.id === highlight.id);
              if (ocrHighlight && onHighlightClick) {
                onHighlightClick(ocrHighlight);
              }
            }}
            className="cursor-pointer"
          >
            {component}
          </div>
        </Popup>
      );
    },
    [highlights, onHighlightClick, onHighlightHover]
  );

  return (
    <div className="h-full w-full">
      <PdfLoader
        url={url}
        beforeLoad={<div className="flex h-full items-center justify-center">Loading PDF...</div>}
      >
        {(pdfDocument) => (
          <PdfHighlighter
            pdfDocument={pdfDocument}
            enableAreaSelection={() => false} // Disable manual selection for now
            onScrollChange={() => {}}
            scrollRef={() => {}}
            onSelectionFinished={() => null} // No manual highlighting for now
            highlightTransform={handleHighlightTransform}
            highlights={pdfHighlights}
          />
        )}
      </PdfLoader>
    </div>
  );
}
