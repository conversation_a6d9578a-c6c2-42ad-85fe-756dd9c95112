'use client';

import { DocumentType } from '@/types/material';
import { DocsViewer } from './doc-viewer';
import { DocumentViewerEnhanced } from './document-viewer-enhanced';
import { PDFViewer, PDFViewerOptions } from './pdf-viewer';

export type DocumentViewerOptions = PDFViewerOptions & {
  enableOCRHighlights?: boolean;
  documentId?: string;
  fileName?: string;
  onTextReference?: (text: string, context?: Record<string, unknown>) => void;
};

type DocumentViewerProps = {
  url?: string;
  fileType?: DocumentType;
  options?: DocumentViewerOptions;
};

export function DocumentViewer({ url, fileType, options }: DocumentViewerProps) {
  if (!url || !fileType) return null;

  if (fileType === DocumentType.PDF) {
    // Use enhanced viewer if OCR features are requested and documentId is provided
    if (options?.enableOCRHighlights && options?.documentId) {
      return (
        <DocumentViewerEnhanced
          documentId={options.documentId}
          documentUrl={url}
          fileName={options.fileName || 'Document'}
          onTextReference={options.onTextReference}
        />
      );
    } else {
      return <PDFViewer url={url} options={options} />;
    }
  } else {
    return <DocsViewer url={url} />;
  }
}
