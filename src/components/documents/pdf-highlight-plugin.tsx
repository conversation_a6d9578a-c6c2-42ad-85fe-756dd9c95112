import { Plugin, type PluginRenderPageLayer } from '@react-pdf-viewer/core';
import React, { useState } from 'react';
import { type OCRHighlight } from './enhanced-pdf-viewer';

interface HighlightPluginProps {
  highlights: OCRHighlight[];
  onHighlightClick?: (highlight: OCRHighlight) => void;
  onHighlightHover?: (highlight: OCRHighlight | null) => void;
  enableInteractions?: boolean;
}

interface HighlightOverlayProps {
  highlights: OCRHighlight[];
  pageIndex: number;
  onHighlightClick?: (highlight: OCRHighlight) => void;
  onHighlightHover?: (highlight: OCRHighlight | null) => void;
  enableInteractions?: boolean;
}

const HighlightOverlay: React.FC<HighlightOverlayProps> = ({
  highlights,
  pageIndex,
  onHighlightClick,
  onHighlightHover,
  enableInteractions = true,
}) => {
  const pageHighlights = highlights.filter((h) => h.pageIndex === pageIndex);
  const [hoveredHighlightId, setHoveredHighlightId] = useState<string | null>(null);

  if (pageHighlights.length === 0) {
    return null;
  }

  const getHighlightStyle = (highlight: OCRHighlight, isHovered: boolean) => {
    const { boundingBox, confidence, layoutType } = highlight;

    // Convert normalized coordinates to percentages
    const left = `${boundingBox.left * 100}%`;
    const top = `${boundingBox.top * 100}%`;
    const width = `${boundingBox.width * 100}%`;
    const height = `${boundingBox.height * 100}%`;

    // Color based on confidence - only show when hovered
    let backgroundColor: string;
    let borderColor: string;

    if (isHovered) {
      if (confidence >= 0.9) {
        backgroundColor = 'rgba(34, 197, 94, 0.1)'; // Green
        borderColor = '#16a34a';
      } else if (confidence >= 0.7) {
        backgroundColor = 'rgba(234, 179, 8, 0.1)'; // Yellow
        borderColor = '#ca8a04';
      } else {
        backgroundColor = 'rgba(239, 68, 68, 0.1)'; // Red
        borderColor = '#dc2626';
      }

      // Layout type specific adjustments
      if (layoutType === 'TITLE' || layoutType === 'LAYOUT_TITLE') {
        backgroundColor = 'rgba(147, 51, 234, 0.4)'; // Purple for titles
        borderColor = '#7c3aed';
      }
    } else {
      // Transparent when not hovered
      backgroundColor = 'transparent';
      borderColor = 'transparent';
    }

    return {
      position: 'absolute' as const,
      left,
      top,
      width,
      height,
      backgroundColor,
      border: `1px solid ${borderColor}`,
      borderRadius: '2px',
      cursor: enableInteractions ? 'pointer' : 'default',
      pointerEvents: (enableInteractions ? 'auto' : 'none') as React.CSSProperties['pointerEvents'],
      transition: 'all 0.2s ease-in-out',
      zIndex: 10,
    };
  };

  return (
    <div className="pointer-events-none absolute inset-0">
      {pageHighlights.map((highlight) => {
        const isHovered = hoveredHighlightId === highlight.id;
        return (
          <div
            key={highlight.id}
            style={getHighlightStyle(highlight, isHovered)}
            onClick={() => enableInteractions && onHighlightClick?.(highlight)}
            onMouseEnter={() => {
              if (enableInteractions) {
                setHoveredHighlightId(highlight.id);
                onHighlightHover?.(highlight);
              }
            }}
            onMouseLeave={() => {
              if (enableInteractions) {
                setHoveredHighlightId(null);
                onHighlightHover?.(null);
              }
            }}
          ></div>
        );
      })}
    </div>
  );
};

export const createHighlightPlugin = ({
  highlights,
  onHighlightClick,
  onHighlightHover,
  enableInteractions = true,
}: HighlightPluginProps): Plugin => {
  const renderPageLayer = (props: PluginRenderPageLayer) => {
    return (
      <div
        style={{
          position: 'absolute',
          left: 0,
          top: 0,
          width: props.width,
          height: props.height,
          zIndex: 1,
          pointerEvents: 'none',
        }}
      >
        <HighlightOverlay
          highlights={highlights}
          pageIndex={props.pageIndex}
          onHighlightClick={onHighlightClick}
          onHighlightHover={onHighlightHover}
          enableInteractions={enableInteractions}
        />
      </div>
    );
  };

  return { renderPageLayer };
};
